import {PokerPlanningSession} from '@/src/server/PokerPlanning/domain/PokerPlanningSession/PokerPlanningSession';
import {PokerPlanningSessionRepository} from '@/src/server/PokerPlanning/application/ports/PokerPlanningSessionRepository';

export class InMemoryPokerPlanningSessionRepository implements PokerPlanningSessionRepository {
    constructor(private readonly sessions: Record<string, PokerPlanningSession>) {}

    async findById(id: string): Promise<PokerPlanningSession | null> {
        return this.sessions[id] ?? null;
    }
}
