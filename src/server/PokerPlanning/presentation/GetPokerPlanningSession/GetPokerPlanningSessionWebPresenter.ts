import {GetPokerPlanningSessionPresenter} from '../../application/ports/GetPokerPlanningSessionPresenter';
import {GetPokerPlanningSessionViewModel} from './GetPokerPlanningSessionViewModel';
import {
  GetPokerPlanningSessionResponse
} from "@/src/server/PokerPlanning/application/queries/GetPokerPlanningSession/GetPokerPlanningSessionResponse";

export class GetPokerPlanningSessionWebPresenter implements GetPokerPlanningSessionPresenter {
  private viewModel: GetPokerPlanningSessionViewModel = {
    session: null,
    error: '',
  };

  presentPokerPlanningSession(session: GetPokerPlanningSessionResponse): void {
    this.viewModel.session = session;
    this.viewModel.error = '';
  }

  presentPokerPlanningSessionNotFound(): void {
    this.viewModel.error = 'Session not found';
    this.viewModel.session = null;
  }

  getViewModel(): GetPokerPlanningSessionViewModel {
    return this.viewModel;
  }
}
