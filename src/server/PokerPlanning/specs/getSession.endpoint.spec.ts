import {describe, expect, it} from 'vitest';
import schema from '@/convex/schema';
import {api} from '@/convex/_generated/api';
import {convexTest} from "convex-test";
import {createFakeSession} from "@/src/server/PokerPlanning/specs/helpers/createFakeSession";

describe('When calling getSession endpoint', () => {
    it('should return the session data', async () => {
        // Arrange
        const t = convexTest(schema);
        const storedSession = createFakeSession();
        const sessionId = await t.run(async ctx =>
            ctx.db.insert('sessions', {
                participants: storedSession.participants,
                tickets: storedSession.tickets,
            }),
        );

        // Act
        const { session } = await t.query(api.queries.pokerPlanning.getPokerPlanningSession, { sessionId });

        // Assert
        expect(session?.participants).toHaveLength(1);
        expect(session?.tickets).toHaveLength(1);
    });
});
