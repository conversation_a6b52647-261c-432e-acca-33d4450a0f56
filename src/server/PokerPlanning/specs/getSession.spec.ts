import {describe, it, expect} from 'vitest';
import { GetPokerPlanningSessionQueryHandler } from '@/src/server/PokerPlanning/application/queries/GetPokerPlanningSession/GetPokerPlanningSessionQueryHandler';
import { InMemoryPokerPlanningSessionRepository } from '@/src/server/PokerPlanning/infrastructure/InMemoryPokerPlanningSessionRepository';
import { GetPokerPlanningSessionWebPresenter } from '@/src/server/PokerPlanning/presentation/GetPokerPlanningSession/GetPokerPlanningSessionWebPresenter';
import type { GetPokerPlanningSessionQuery } from '@/src/server/PokerPlanning/application/queries/GetPokerPlanningSession/GetPokerPlanningSessionQuery';
import {createFakeSession} from "@/src/server/PokerPlanning/specs/helpers/createFakeSession";

describe('When getting a poker planning session', () => {
    it('should return participants and tickets', async () => {
        // Arrange
        const storedSession = createFakeSession();
        const sessions = { 'session-1': storedSession };
        const repository = new InMemoryPokerPlanningSessionRepository(sessions);
        const handler = new GetPokerPlanningSessionQueryHandler(repository);
        const presenter = new GetPokerPlanningSessionWebPresenter();
        const query: GetPokerPlanningSessionQuery = { sessionId: 'session-1' };

        // Act
        await handler.execute(query, presenter);
        const { session } = presenter.getViewModel();

        // Assert
        expect(session?.participants).toHaveLength(1);
        expect(session?.tickets).toHaveLength(1);
    });

    it('should present an error when session is missing', async () => {
        // Arrange
        const repository = new InMemoryPokerPlanningSessionRepository({});
        const handler = new GetPokerPlanningSessionQueryHandler(repository);
        const presenter = new GetPokerPlanningSessionWebPresenter();
        const query: GetPokerPlanningSessionQuery = { sessionId: 'unknown' };

        // Act
        await handler.execute(query, presenter);
        const { session, error } = presenter.getViewModel();

        // Assert
        expect(session).toBeNull();
        expect(error).toBe('Session not found');
    });
});
