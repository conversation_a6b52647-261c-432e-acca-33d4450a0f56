import {FC, PropsWithChildren} from "react";
import {ReduxProvider} from "@/src/client/PokerPlanning/infrastructure/nextjs/providers/ReduxProvider";
import {Theme} from "@radix-ui/themes";
import {
  TanstackQueryClientProvider
} from "@/src/client/PokerPlanning/infrastructure/nextjs/providers/TanstackQueryClientProvider";

const AppLayout: FC<PropsWithChildren> = ({children}) => {
  return (
    <TanstackQueryClientProvider>
      <ReduxProvider>
        <Theme appearance="light"
               accentColor="indigo"
               grayColor="slate"
               radius="small"
               hasBackground={true}
               panelBackground="translucent"
        >
          {children}
        </Theme>
      </ReduxProvider>
    </TanstackQueryClientProvider>
  );
};

export default AppLayout;