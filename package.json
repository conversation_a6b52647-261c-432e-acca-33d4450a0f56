{"name": "nextjs-15-clean-architecture-template", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "dev:all": "concurrently \"npm run dev\" \"npm run convex:dev\"", "convex:dev": "convex dev", "convex:deploy": "convex deploy", "build": "next build", "start": "next start", "lint": "next lint", "deps:all": "npm run deps:screenshot && npm run deps:graph && npm run deps:github && npm run deps:server && npm run deps:client", "deps:github": "node tools/github.js", "deps:graph": "npx depcruise . --include-only \"^src|^app\" --output-type dot | dot -T svg | depcruise-wrap-stream-in-html > tools/index.html", "deps:screenshot": "npx depcruise . --include-only \"^src|^app\" --output-type dot | dot -T svg > assets/deps.svg", "deps:server": "npx depcruise . --include-only \"^src/server|^app\" --output-type dot | dot -T svg | depcruise-wrap-stream-in-html > tools/server-dependency-graph.html", "deps:client": "npx depcruise . --include-only \"^src/client|^app\" --output-type dot | dot -T svg | depcruise-wrap-stream-in-html > tools/client-dependency-graph.html", "test": "vitest run", "test:coverage": "vitest run --coverage"}, "dependencies": {"@auth/core": "^0.37.0", "@convex-dev/auth": "^0.0.85", "@formkit/auto-animate": "^0.8.2", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/themes": "^3.2.1", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.83.0", "convex": "^1.23.0", "convex-helpers": "^0.1.83", "lucide-react": "^0.525.0", "@reduxjs/toolkit": "^2.8.2", "react-redux": "^9.2.0", "next": "^15.4.2", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/node": "^24.0.15", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.4.1", "@vitest/coverage-v8": "^3.2.4", "concurrently": "^9.1.2", "convex-test": "^0.0.36", "dependency-cruiser": "^16.10.4", "eslint": "^9.16.0", "eslint-config-next": "15.1.8", "jest-extended": "^4.0.2", "jsdom": "^25.0.1", "msw": "^2.8.7", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.1.4", "vitest-mock-extended": "^2.0.2"}}